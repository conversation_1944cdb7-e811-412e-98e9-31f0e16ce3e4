#!/usr/bin/env python3
"""
简化的测试脚本，用于验证 cursor_clean.py 的功能
"""

import os
import tempfile
import shutil
import configparser
from unittest.mock import patch
import cursor_clean

def test_is_admin():
    """测试管理员权限检查功能"""
    print("测试管理员权限检查...")
    
    # 模拟管理员权限为True
    with patch('cursor_clean.ctypes.windll.shell32.IsUserAnAdmin', return_value=True):
        result = cursor_clean.is_admin()
        assert result == True, "管理员权限检查失败 - 应该返回True"
        print("✓ 管理员权限检查 (True) - 通过")
    
    # 模拟管理员权限为False
    with patch('cursor_clean.ctypes.windll.shell32.IsUserAnAdmin', return_value=False):
        result = cursor_clean.is_admin()
        assert result == False, "管理员权限检查失败 - 应该返回False"
        print("✓ 管理员权限检查 (False) - 通过")
    
    # 模拟异常情况
    with patch('cursor_clean.ctypes.windll.shell32.IsUserAnAdmin', side_effect=Exception("Test")):
        result = cursor_clean.is_admin()
        assert result == False, "管理员权限检查失败 - 异常时应该返回False"
        print("✓ 管理员权限检查 (异常) - 通过")

def test_config_functions():
    """测试配置文件相关功能"""
    print("\n测试配置文件功能...")
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp()
    config_path = os.path.join(test_dir, 'config.env')
    
    try:
        # 测试创建默认配置
        with patch('cursor_clean.getpass.getuser', return_value='testuser'):
            with patch('cursor_clean.os.path.dirname', return_value=test_dir):
                with patch('cursor_clean.os.path.abspath', return_value='/test/path'):
                    cursor_clean.create_default_config(config_path)
        
        # 验证配置文件是否创建
        assert os.path.exists(config_path), "配置文件未创建"
        print("✓ 创建默认配置文件 - 通过")
        
        # 验证配置内容
        config = configparser.ConfigParser()
        config.read(config_path)
        expected_path = os.path.join('C:\\Users', 'testuser', 'AppData\\Roaming\\Cursor\\User')
        assert config['PATHS']['base_path'] == expected_path, "配置内容不正确"
        print("✓ 配置文件内容验证 - 通过")
        
        # 测试读取配置
        with patch('cursor_clean.os.path.dirname', return_value=test_dir):
            with patch('cursor_clean.os.path.abspath', return_value='/test/path'):
                result_config = cursor_clean.read_config()
                assert result_config['PATHS']['base_path'] == expected_path, "读取配置失败"
                print("✓ 读取配置文件 - 通过")
    
    finally:
        # 清理临时目录
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def test_clean_functionality():
    """测试清理功能"""
    print("\n测试清理功能...")
    
    # 创建测试目录结构
    test_dir = tempfile.mkdtemp()
    base_path = os.path.join(test_dir, 'cursor_test')
    os.makedirs(base_path, exist_ok=True)
    
    try:
        # 创建测试文件和文件夹结构
        # globalStorage文件夹和文件
        global_storage = os.path.join(base_path, 'globalStorage')
        os.makedirs(global_storage, exist_ok=True)
        
        state_file = os.path.join(global_storage, 'state.vscdb')
        backup_file = os.path.join(global_storage, 'state.vscdb.backup')
        
        with open(state_file, 'w') as f:
            f.write('test content')
        with open(backup_file, 'w') as f:
            f.write('backup content')
        
        # History文件夹和内容
        history_dir = os.path.join(base_path, 'History')
        os.makedirs(history_dir, exist_ok=True)
        history_file = os.path.join(history_dir, 'test_file.txt')
        with open(history_file, 'w') as f:
            f.write('history content')
        
        # workspaceStorage文件夹
        workspace_storage = os.path.join(base_path, 'workspaceStorage')
        os.makedirs(workspace_storage, exist_ok=True)
        workspace_file = os.path.join(workspace_storage, 'workspace_file.txt')
        with open(workspace_file, 'w') as f:
            f.write('workspace content')
        
        # 验证文件存在
        assert os.path.exists(state_file), "测试文件未创建"
        assert os.path.exists(backup_file), "测试备份文件未创建"
        assert os.path.exists(history_file), "测试历史文件未创建"
        assert os.path.exists(workspace_file), "测试工作区文件未创建"
        print("✓ 测试文件结构创建 - 通过")
        
        # 模拟配置并执行清理
        mock_config = configparser.ConfigParser()
        mock_config['PATHS'] = {'base_path': base_path}
        
        with patch('cursor_clean.read_config', return_value=mock_config):
            cursor_clean.clean_cursor_files()
        
        # 验证清理结果
        assert not os.path.exists(state_file), "state.vscdb 文件未被删除"
        assert not os.path.exists(backup_file), "state.vscdb.backup 文件未被删除"
        print("✓ 删除指定文件 - 通过")
        
        # 验证History文件夹被清空但文件夹本身存在
        assert os.path.exists(history_dir), "History文件夹被错误删除"
        assert len(os.listdir(history_dir)) == 0, "History文件夹未被清空"
        print("✓ 清空History文件夹 - 通过")
        
        # 验证workspaceStorage文件夹被完全删除
        assert not os.path.exists(workspace_storage), "workspaceStorage文件夹未被删除"
        print("✓ 删除workspaceStorage文件夹 - 通过")
    
    finally:
        # 清理临时目录
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def main():
    """运行所有测试"""
    print("开始测试 cursor_clean.py...")
    print("=" * 50)
    
    try:
        test_is_admin()
        test_config_functions()
        test_clean_functionality()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！")
        print("cursor_clean.py 的功能验证完成。")
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"\n💥 测试出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
