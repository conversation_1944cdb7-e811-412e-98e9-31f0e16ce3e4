import unittest
import os
import tempfile
import shutil
import configparser
from unittest.mock import patch

# 导入要测试的模块
import cursor_clean


class TestCursorClean(unittest.TestCase):
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.test_dir, 'config.env')
        
    def tearDown(self):
        """测试后的清理工作"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    @patch('cursor_clean.ctypes.windll.shell32.IsUserAnAdmin')
    def test_is_admin_true(self, mock_is_admin):
        """测试管理员权限检查 - 返回True"""
        mock_is_admin.return_value = True
        self.assertTrue(cursor_clean.is_admin())
    
    @patch('cursor_clean.ctypes.windll.shell32.IsUserAnAdmin')
    def test_is_admin_false(self, mock_is_admin):
        """测试管理员权限检查 - 返回False"""
        mock_is_admin.return_value = False
        self.assertFalse(cursor_clean.is_admin())
    
    @patch('cursor_clean.ctypes.windll.shell32.IsUserAnAdmin')
    def test_is_admin_exception(self, mock_is_admin):
        """测试管理员权限检查 - 异常情况"""
        mock_is_admin.side_effect = Exception("Test exception")
        self.assertFalse(cursor_clean.is_admin())
    
    @patch('cursor_clean.getpass.getuser')
    @patch('cursor_clean.os.path.dirname')
    @patch('cursor_clean.os.path.abspath')
    def test_create_default_config(self, mock_abspath, mock_dirname, mock_getuser):
        """测试创建默认配置文件"""
        mock_getuser.return_value = 'testuser'
        mock_abspath.return_value = '/test/path'
        mock_dirname.return_value = self.test_dir
        
        cursor_clean.create_default_config(self.config_path)
        
        # 验证配置文件是否创建
        self.assertTrue(os.path.exists(self.config_path))
        
        # 验证配置内容
        config = configparser.ConfigParser()
        config.read(self.config_path)
        expected_path = os.path.join('C:\\Users', 'testuser', 'AppData\\Roaming\\Cursor\\User')
        self.assertEqual(config['PATHS']['base_path'], expected_path)
    
    @patch('cursor_clean.os.path.dirname')
    @patch('cursor_clean.os.path.abspath')
    def test_read_config_file_exists(self, mock_dirname, mock_abspath):
        """测试读取已存在的配置文件"""
        mock_abspath.return_value = '/test/path'
        mock_dirname.return_value = self.test_dir

        # 创建测试配置文件
        config = configparser.ConfigParser()
        config['PATHS'] = {'base_path': '/test/cursor/path'}
        with open(self.config_path, 'w') as f:
            config.write(f)

        result_config = cursor_clean.read_config()

        # 验证读取的配置正确
        self.assertEqual(result_config['PATHS']['base_path'], '/test/cursor/path')
    
    @patch('cursor_clean.os.path.dirname')
    @patch('cursor_clean.os.path.abspath')
    @patch('cursor_clean.create_default_config')
    def test_read_config_file_not_exists(self, mock_create_config, mock_dirname, mock_abspath):
        """测试读取不存在的配置文件"""
        mock_abspath.return_value = '/test/path'
        mock_dirname.return_value = self.test_dir
        
        # 确保配置文件不存在
        if os.path.exists(self.config_path):
            os.remove(self.config_path)
        
        cursor_clean.read_config()
        
        # 验证会调用创建默认配置
        mock_create_config.assert_called_once()


class TestCursorCleanIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_dir = tempfile.mkdtemp()
        self.base_path = os.path.join(self.test_dir, 'cursor_test')
        os.makedirs(self.base_path, exist_ok=True)
        
        # 创建测试文件和文件夹结构
        self.setup_test_structure()
    
    def tearDown(self):
        """测试后的清理工作"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def setup_test_structure(self):
        """创建测试用的文件和文件夹结构"""
        # 创建globalStorage文件夹和文件
        global_storage = os.path.join(self.base_path, 'globalStorage')
        os.makedirs(global_storage, exist_ok=True)
        
        # 创建要删除的文件
        with open(os.path.join(global_storage, 'state.vscdb'), 'w') as f:
            f.write('test content')
        with open(os.path.join(global_storage, 'state.vscdb.backup'), 'w') as f:
            f.write('backup content')
        
        # 创建History文件夹和内容
        history_dir = os.path.join(self.base_path, 'History')
        os.makedirs(history_dir, exist_ok=True)
        with open(os.path.join(history_dir, 'test_file.txt'), 'w') as f:
            f.write('history content')
        
        # 创建workspaceStorage文件夹
        workspace_storage = os.path.join(self.base_path, 'workspaceStorage')
        os.makedirs(workspace_storage, exist_ok=True)
        with open(os.path.join(workspace_storage, 'workspace_file.txt'), 'w') as f:
            f.write('workspace content')
    
    @patch('cursor_clean.read_config')
    def test_clean_cursor_files(self, mock_read_config):
        """测试清理Cursor文件功能"""
        # 模拟配置 - 使用字典而不是MagicMock
        mock_config = configparser.ConfigParser()
        mock_config['PATHS'] = {'base_path': self.base_path}
        mock_read_config.return_value = mock_config

        # 执行清理
        cursor_clean.clean_cursor_files()

        # 验证文件是否被删除
        self.assertFalse(os.path.exists(os.path.join(self.base_path, 'globalStorage', 'state.vscdb')))
        self.assertFalse(os.path.exists(os.path.join(self.base_path, 'globalStorage', 'state.vscdb.backup')))

        # 验证History文件夹是否被清空但文件夹本身存在
        history_dir = os.path.join(self.base_path, 'History')
        self.assertTrue(os.path.exists(history_dir))
        self.assertEqual(len(os.listdir(history_dir)), 0)

        # 验证workspaceStorage文件夹是否被完全删除
        self.assertFalse(os.path.exists(os.path.join(self.base_path, 'workspaceStorage')))


if __name__ == '__main__':
    unittest.main(verbosity=2)
